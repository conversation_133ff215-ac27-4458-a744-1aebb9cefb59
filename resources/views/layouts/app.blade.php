<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Edunetra') }} - @yield('title', 'Visual Impairment Solutions: Inclusive Opportunities and navigation for English Learning')</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- Custom Styles -->
        <style>
            .bg-edunetra-yellow { background-color: #FFD700; }
            .text-edunetra-yellow { color: #FFD700; }
            .bg-edunetra-dark { background-color: #2D3748; }
            .hero-bg {
                background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                           url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23f0f0f0" width="1200" height="600"/><text x="50%" y="50%" font-family="Arial" font-size="48" fill="%23666" text-anchor="middle" dy=".3em">VISION Background Image</text></svg>');
                background-size: cover;
                background-position: center;
            }
        </style>

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-50">
            @include('layouts.navigation')

            <!-- Page Content -->
            <main>
                @yield('content')
                {{ $slot ?? '' }}
            </main>

            @include('layouts.footer')
        </div>
    </body>
</html>
