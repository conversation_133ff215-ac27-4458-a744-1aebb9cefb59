@extends('layouts.app')

@section('title', 'Visual Impairment Solutions: Inclusive Opportunities and navigation for English Learning')

@section('content')
<!-- Hero Section with VISION -->
<section class="hero-bg min-h-screen flex items-center justify-center text-white relative">
    <div class="absolute inset-0 bg-black bg-opacity-40"></div>
    <div class="relative z-10 text-center max-w-4xl mx-auto px-4">
        <h1 class="text-6xl md:text-8xl font-bold mb-6 tracking-wider">
            VISION
        </h1>
        <p class="text-xl md:text-2xl mb-8 font-light">
            English for Young Learner
        </p>
        <p class="text-lg mb-8 max-w-2xl mx-auto leading-relaxed">
            Visual Impairment Solutions: Inclusive Opportunities and navigation for English Learning
        </p>
        
        <!-- Search Bar -->
        <div class="max-w-md mx-auto mb-8">
            <div class="relative">
                <input type="text" placeholder="Search courses..." 
                       class="w-full px-4 py-3 rounded-lg text-black focus:outline-none focus:ring-2 focus:ring-edunetra-yellow">
                <button class="absolute right-2 top-2 bg-edunetra-yellow text-black px-4 py-2 rounded-md hover:bg-yellow-400 transition duration-150">
                    🔍
                </button>
            </div>
        </div>

        <!-- Course Management Button for Teachers -->
        @auth
            @if(auth()->user()->isTeacher() || auth()->user()->isAdmin())
                <div class="mb-8">
                    <a href="#" class="bg-edunetra-yellow text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition duration-150 inline-flex items-center">
                        ⚙️ Course Management
                    </a>
                </div>
            @endif
        @endauth
    </div>
</section>

<!-- Session Navigation -->
<section class="bg-edunetra-yellow py-4">
    <div class="max-w-7xl mx-auto px-4">
        <div class="flex flex-wrap justify-center gap-4">
            <a href="#" class="bg-white text-black px-4 py-2 rounded-md font-medium hover:bg-gray-100 transition duration-150">PENDAHULUAN</a>
            @for($i = 1; $i <= 8; $i++)
                <a href="#" class="bg-white text-black px-4 py-2 rounded-md font-medium hover:bg-gray-100 transition duration-150">SESI {{ $i }}</a>
            @endfor
        </div>
    </div>
</section>

<!-- Featured Courses Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Courses</h2>
            <p class="text-lg text-gray-600">Discover our most popular learning programs</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredCourses as $course)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                    <div class="h-48 bg-gray-200 flex items-center justify-center">
                        @if($course->image)
                            <img src="{{ $course->image }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                        @else
                            <div class="text-gray-500 text-center">
                                <div class="text-4xl mb-2">📚</div>
                                <p>{{ $course->title }}</p>
                            </div>
                        @endif
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $course->title }}</h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">{{ Str::limit($course->description, 100) }}</p>
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">by {{ $course->teacher->name }}</span>
                            <span class="bg-edunetra-yellow text-black px-2 py-1 rounded text-sm font-medium">
                                {{ ucfirst($course->level) }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-gray-900">
                                @if($course->price > 0)
                                    Rp {{ number_format($course->price, 0, ',', '.') }}
                                @else
                                    Free
                                @endif
                            </span>
                            <a href="{{ route('courses.show', $course->slug) }}" 
                               class="bg-edunetra-yellow text-black px-4 py-2 rounded-md font-medium hover:bg-yellow-400 transition duration-150">
                                View Course
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="text-center mt-12">
            <a href="{{ route('courses.public') }}" 
               class="bg-edunetra-dark text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-150">
                View All Courses
            </a>
        </div>
    </div>
</section>

<!-- Announcements Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Latest Announcements</h2>
            <p class="text-lg text-gray-600">Stay updated with our latest news and opportunities</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($announcements as $announcement)
                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition duration-300">
                    <div class="flex items-center mb-4">
                        <span class="bg-edunetra-yellow text-black px-3 py-1 rounded-full text-sm font-medium">
                            {{ ucwords(str_replace('_', ' ', $announcement->type)) }}
                        </span>
                        @if($announcement->is_featured)
                            <span class="ml-2 text-yellow-500">⭐</span>
                        @endif
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $announcement->title }}</h3>
                    <p class="text-gray-600 mb-4 line-clamp-3">{{ Str::limit($announcement->content, 120) }}</p>
                    <div class="text-sm text-gray-500">
                        {{ $announcement->published_at->format('M d, Y') }}
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Special Programs -->
        <div class="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-blue-100 rounded-lg p-6 text-center hover:bg-blue-200 transition duration-300">
                <div class="text-3xl mb-3">ℹ️</div>
                <h4 class="font-semibold text-blue-900 mb-2">New Info</h4>
                <p class="text-sm text-blue-700">Latest updates and information</p>
            </div>
            <div class="bg-green-100 rounded-lg p-6 text-center hover:bg-green-200 transition duration-300">
                <div class="text-3xl mb-3">🤝</div>
                <h4 class="font-semibold text-green-900 mb-2">Volunteer</h4>
                <p class="text-sm text-green-700">Join our volunteer programs</p>
            </div>
            <div class="bg-purple-100 rounded-lg p-6 text-center hover:bg-purple-200 transition duration-300">
                <div class="text-3xl mb-3">💼</div>
                <h4 class="font-semibold text-purple-900 mb-2">Open Recruitment</h4>
                <p class="text-sm text-purple-700">Career opportunities</p>
            </div>
            <div class="bg-orange-100 rounded-lg p-6 text-center hover:bg-orange-200 transition duration-300">
                <div class="text-3xl mb-3">👨‍🏫</div>
                <h4 class="font-semibold text-orange-900 mb-2">Shadow Teacher</h4>
                <p class="text-sm text-orange-700">Teaching experience program</p>
            </div>
        </div>

        <!-- LENTERA Program -->
        <div class="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white text-center">
            <h3 class="text-2xl font-bold mb-4">LENTERA</h3>
            <p class="text-lg mb-4">Layanan Pendampingan Mobilitas Difabel Netra</p>
            <p class="mb-6">Specialized support services for visually impaired learners</p>
            <a href="#" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-150">
                Learn More
            </a>
        </div>
    </div>
</section>
@endsection
