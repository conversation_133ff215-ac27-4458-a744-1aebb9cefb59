@extends('layouts.app')

@section('title', 'All Courses - Edunetra')

@section('content')
<!-- <PERSON> Header -->
<section class="bg-edunetra-dark text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl font-bold mb-4">All Courses</h1>
        <p class="text-xl text-gray-300">Explore our comprehensive learning programs</p>
    </div>
</section>

<!-- Filters and Search -->
<section class="py-8 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap gap-4 items-center justify-between">
            <!-- Search -->
            <div class="flex-1 max-w-md">
                <input type="text" placeholder="Search courses..." 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-edunetra-yellow">
            </div>
            
            <!-- Filters -->
            <div class="flex gap-4">
                <select class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-edunetra-yellow">
                    <option value="">All Levels</option>
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                </select>
                
                <select class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-edunetra-yellow">
                    <option value="">All Categories</option>
                    <option value="english">English</option>
                    <option value="programming">Programming</option>
                    <option value="design">Design</option>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Courses Grid -->
<section class="py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($courses->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($courses as $course)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                        <div class="h-48 bg-gray-200 flex items-center justify-center">
                            @if($course->image)
                                <img src="{{ $course->image }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                            @else
                                <div class="text-gray-500 text-center">
                                    <div class="text-4xl mb-2">📚</div>
                                    <p class="text-sm">{{ $course->title }}</p>
                                </div>
                            @endif
                        </div>
                        
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="bg-edunetra-yellow text-black px-2 py-1 rounded text-xs font-medium">
                                    {{ ucfirst($course->level) }}
                                </span>
                                <span class="text-xs text-gray-500">
                                    {{ $course->duration_weeks }} weeks
                                </span>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ $course->title }}</h3>
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ Str::limit($course->description, 80) }}</p>
                            
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-xs text-gray-500">by {{ $course->teacher->name }}</span>
                                <div class="flex items-center text-xs text-gray-500">
                                    <span>👥 {{ $course->getEnrolledStudentsCount() }}</span>
                                    @if($course->max_students)
                                        <span>/{{ $course->max_students }}</span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-lg font-bold text-gray-900">
                                    @if($course->price > 0)
                                        Rp {{ number_format($course->price, 0, ',', '.') }}
                                    @else
                                        Free
                                    @endif
                                </span>
                                <a href="{{ route('courses.show', $course->slug) }}" 
                                   class="bg-edunetra-yellow text-black px-3 py-1 rounded text-sm font-medium hover:bg-yellow-400 transition duration-150">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="mt-12">
                {{ $courses->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="text-6xl mb-4">📚</div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No courses found</h3>
                <p class="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
        @endif
    </div>
</section>
@endsection
