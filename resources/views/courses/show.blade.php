@extends('layouts.app')

@section('title', $course->title . ' - Edunetra')

@section('content')
<!-- Course Header -->
<section class="bg-edunetra-dark text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
                <div class="flex items-center mb-4">
                    <span class="bg-edunetra-yellow text-black px-3 py-1 rounded-full text-sm font-medium mr-3">
                        {{ ucfirst($course->level) }}
                    </span>
                    <span class="text-gray-300">{{ $course->duration_weeks }} weeks</span>
                </div>
                <h1 class="text-4xl font-bold mb-4">{{ $course->title }}</h1>
                <p class="text-xl text-gray-300 mb-6">{{ $course->description }}</p>
                <div class="flex items-center text-gray-300 mb-6">
                    <span class="mr-6">👨‍🏫 {{ $course->teacher->name }}</span>
                    <span class="mr-6">👥 {{ $course->getEnrolledStudentsCount() }} students</span>
                    <span>📅 {{ $course->start_date ? $course->start_date->format('M d, Y') : 'Flexible start' }}</span>
                </div>
            </div>
            <div class="bg-white rounded-lg p-6 text-black">
                <div class="text-center mb-6">
                    <div class="text-3xl font-bold text-edunetra-dark mb-2">
                        @if($course->price > 0)
                            Rp {{ number_format($course->price, 0, ',', '.') }}
                        @else
                            Free
                        @endif
                    </div>
                    <p class="text-gray-600">Full course access</p>
                </div>
                
                @auth
                    @if($isEnrolled)
                        <div class="text-center">
                            <div class="bg-green-100 text-green-800 px-4 py-2 rounded-lg mb-4">
                                ✅ You are enrolled in this course
                            </div>
                            <div class="mb-4">
                                <div class="text-sm text-gray-600 mb-1">Progress</div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-edunetra-yellow h-2 rounded-full" style="width: {{ $enrollment->progress }}%"></div>
                                </div>
                                <div class="text-sm text-gray-600 mt-1">{{ $enrollment->progress }}% complete</div>
                            </div>
                            <a href="#sessions" class="w-full bg-edunetra-yellow text-black py-3 px-6 rounded-lg font-semibold hover:bg-yellow-400 transition duration-150 block text-center">
                                Continue Learning
                            </a>
                        </div>
                    @else
                        <form action="#" method="POST">
                            @csrf
                            <button type="submit" class="w-full bg-edunetra-yellow text-black py-3 px-6 rounded-lg font-semibold hover:bg-yellow-400 transition duration-150">
                                Enroll Now
                            </button>
                        </form>
                    @endif
                @else
                    <div class="text-center">
                        <a href="{{ route('login') }}" class="w-full bg-edunetra-yellow text-black py-3 px-6 rounded-lg font-semibold hover:bg-yellow-400 transition duration-150 block">
                            Login to Enroll
                        </a>
                        <p class="text-sm text-gray-600 mt-2">
                            Don't have an account? <a href="{{ route('register') }}" class="text-edunetra-dark hover:underline">Sign up</a>
                        </p>
                    </div>
                @endauth
            </div>
        </div>
    </div>
</section>

<!-- Course Content -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Course Description -->
                <div class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Course</h2>
                    <div class="prose max-w-none text-gray-600">
                        <p>{{ $course->description }}</p>
                        <p class="mt-4">
                            This comprehensive {{ $course->duration_weeks }}-week program is designed to provide students with 
                            a solid foundation in English learning, specifically tailored for young learners with visual impairments. 
                            Our innovative teaching methods ensure accessibility while maintaining high educational standards.
                        </p>
                    </div>
                </div>

                <!-- Course Sessions -->
                <div id="sessions" class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Course Sessions</h2>
                    <div class="space-y-4">
                        @foreach($course->sessions as $session)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition duration-150">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                            {{ $session->getSessionName() }}: {{ $session->title }}
                                        </h3>
                                        <p class="text-gray-600 mb-2">{{ $session->description }}</p>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <span class="mr-4">⏱️ {{ $session->duration_minutes }} minutes</span>
                                            @if($isEnrolled && $session->isCompleted(auth()->id()))
                                                <span class="text-green-600">✅ Completed</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        @if($isEnrolled)
                                            <a href="#" class="bg-edunetra-yellow text-black px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-400 transition duration-150">
                                                @if($session->isCompleted(auth()->id()))
                                                    Review
                                                @else
                                                    Start
                                                @endif
                                            </a>
                                        @else
                                            <span class="text-gray-400 text-sm">🔒 Locked</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Instructor -->
                <div class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Your Instructor</h2>
                    <div class="flex items-start">
                        <div class="w-16 h-16 bg-edunetra-yellow rounded-full flex items-center justify-center mr-4">
                            <span class="text-black font-bold text-xl">{{ substr($course->teacher->name, 0, 1) }}</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $course->teacher->name }}</h3>
                            <p class="text-gray-600">{{ $course->teacher->bio ?? 'Experienced educator specializing in inclusive learning.' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Course Info -->
                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Course Information</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Duration:</span>
                            <span class="font-medium">{{ $course->duration_weeks }} weeks</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Sessions:</span>
                            <span class="font-medium">{{ $course->sessions->count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level:</span>
                            <span class="font-medium">{{ ucfirst($course->level) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Students:</span>
                            <span class="font-medium">{{ $course->getEnrolledStudentsCount() }}</span>
                        </div>
                        @if($course->start_date)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Start Date:</span>
                                <span class="font-medium">{{ $course->start_date->format('M d, Y') }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Tags -->
                @if($course->tags)
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($course->tags as $tag)
                                <span class="bg-edunetra-yellow text-black px-3 py-1 rounded-full text-sm">{{ $tag }}</span>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Related Courses -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Courses</h3>
                    <div class="space-y-3">
                        <a href="#" class="block p-3 bg-white rounded-lg hover:shadow-md transition duration-150">
                            <h4 class="font-medium text-gray-900 mb-1">Advanced English Grammar</h4>
                            <p class="text-sm text-gray-600">Next level English course</p>
                        </a>
                        <a href="#" class="block p-3 bg-white rounded-lg hover:shadow-md transition duration-150">
                            <h4 class="font-medium text-gray-900 mb-1">English Conversation</h4>
                            <p class="text-sm text-gray-600">Practice speaking skills</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
