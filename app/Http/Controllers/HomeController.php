<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Announcement;

class HomeController extends Controller
{
    public function index()
    {
        $featuredCourses = Course::where('status', 'published')
            ->with('teacher')
            ->latest()
            ->take(6)
            ->get();

        $announcements = Announcement::published()
            ->featured()
            ->latest()
            ->take(5)
            ->get();

        $allAnnouncements = Announcement::published()
            ->latest()
            ->take(10)
            ->get();

        return view('home', compact('featuredCourses', 'announcements', 'allAnnouncements'));
    }

    public function courses()
    {
        $courses = Course::where('status', 'published')
            ->with('teacher')
            ->paginate(12);

        return view('courses.index', compact('courses'));
    }

    public function about()
    {
        return view('about');
    }

    public function contact()
    {
        return view('contact');
    }
}
