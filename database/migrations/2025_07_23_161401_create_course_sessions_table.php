<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->integer('session_number'); // 1-8
            $table->text('content')->nullable();
            $table->json('materials')->nullable(); // Files, videos, etc.
            $table->json('assignments')->nullable();
            $table->boolean('is_published')->default(false);
            $table->integer('duration_minutes')->default(60);
            $table->timestamps();

            $table->unique(['course_id', 'session_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_sessions');
    }
};
