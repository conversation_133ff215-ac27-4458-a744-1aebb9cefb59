<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\CourseSession;
use App\Models\Announcement;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Admin User
        $admin = User::create([
            'name' => 'Admin Edunetra',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'is_active' => true,
        ]);

        // Create Teacher Users
        $teacher1 = User::create([
            'name' => 'Dr. <PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'teacher',
            'bio' => 'Experienced English teacher with 10+ years in language education.',
            'is_active' => true,
        ]);

        $teacher2 = User::create([
            'name' => 'Prof<PERSON> <PERSON>',
            'email' => 'micha<PERSON>@edunetra.id',
            'password' => Hash::make('password'),
            'role' => 'teacher',
            'bio' => 'Computer Science professor specializing in web development.',
            'is_active' => true,
        ]);

        // Create Student Users
        $student1 = User::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
            'is_active' => true,
        ]);

        $student2 = User::create([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
            'is_active' => true,
        ]);

        // Create Courses
        $course1 = Course::create([
            'title' => 'English for Young Learner',
            'description' => 'Comprehensive English learning program designed for young students to build strong foundation in English language skills.',
            'slug' => 'english-for-young-learner',
            'level' => 'beginner',
            'status' => 'published',
            'duration_weeks' => 8,
            'price' => 500000,
            'teacher_id' => $teacher1->id,
            'max_students' => 20,
            'start_date' => now()->addDays(7),
            'end_date' => now()->addDays(63),
            'tags' => ['English', 'Language', 'Young Learner'],
        ]);

        $course2 = Course::create([
            'title' => 'Web Development Fundamentals',
            'description' => 'Learn the basics of web development including HTML, CSS, and JavaScript.',
            'slug' => 'web-development-fundamentals',
            'level' => 'intermediate',
            'status' => 'published',
            'duration_weeks' => 8,
            'price' => 750000,
            'teacher_id' => $teacher2->id,
            'max_students' => 15,
            'start_date' => now()->addDays(14),
            'end_date' => now()->addDays(70),
            'tags' => ['Programming', 'Web Development', 'Technology'],
        ]);

        // Create Course Sessions for English Course
        for ($i = 1; $i <= 8; $i++) {
            CourseSession::create([
                'course_id' => $course1->id,
                'title' => "Session {$i}: English Fundamentals",
                'description' => "Learning objectives and activities for session {$i}",
                'session_number' => $i,
                'content' => "Detailed content for session {$i} of English for Young Learner course.",
                'is_published' => true,
                'duration_minutes' => 90,
            ]);
        }

        // Create Course Sessions for Web Development Course
        $webSessions = [
            'Introduction to HTML',
            'CSS Styling Basics',
            'JavaScript Fundamentals',
            'DOM Manipulation',
            'Responsive Design',
            'Forms and Validation',
            'APIs and AJAX',
            'Final Project'
        ];

        foreach ($webSessions as $index => $sessionTitle) {
            CourseSession::create([
                'course_id' => $course2->id,
                'title' => "Session " . ($index + 1) . ": {$sessionTitle}",
                'description' => "Learn {$sessionTitle} in this comprehensive session",
                'session_number' => $index + 1,
                'content' => "Detailed content for {$sessionTitle} session.",
                'is_published' => true,
                'duration_minutes' => 120,
            ]);
        }

        // Create Announcements
        Announcement::create([
            'title' => 'New Info: Course Registration Open',
            'content' => 'Registration for new courses is now open. Join our English and Web Development programs!',
            'type' => 'new_info',
            'is_published' => true,
            'is_featured' => true,
            'created_by' => $admin->id,
            'published_at' => now(),
        ]);

        Announcement::create([
            'title' => 'Volunteer Opportunity: Teaching Assistant',
            'content' => 'We are looking for volunteers to assist in our teaching programs. Great opportunity for experience!',
            'type' => 'volunteer',
            'is_published' => true,
            'created_by' => $admin->id,
            'published_at' => now(),
        ]);

        Announcement::create([
            'title' => 'Open Recruitment: New Teachers',
            'content' => 'Edunetra is expanding! We are looking for qualified teachers to join our team.',
            'type' => 'recruitment',
            'is_published' => true,
            'created_by' => $admin->id,
            'published_at' => now(),
        ]);

        Announcement::create([
            'title' => 'Shadow Teacher Program',
            'content' => 'Join our Shadow Teacher program to gain hands-on teaching experience.',
            'type' => 'shadow_teacher',
            'is_published' => true,
            'created_by' => $admin->id,
            'published_at' => now(),
        ]);

        Announcement::create([
            'title' => 'LENTERA: Layanan Pendampingan Mobilitas Difabel Netra',
            'content' => 'Program khusus untuk mendukung pembelajaran bagi penyandang disabilitas netra.',
            'type' => 'lentera',
            'is_published' => true,
            'is_featured' => true,
            'created_by' => $admin->id,
            'published_at' => now(),
        ]);
    }
}
